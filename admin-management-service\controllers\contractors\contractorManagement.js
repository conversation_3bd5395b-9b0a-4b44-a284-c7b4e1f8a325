const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const VerificationRequests = require('../../model/verificationRequests');

// Get all contractors with pagination and filters
exports.getAllContractors = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            verificationStatus,
            search,
            sortBy = 'createdAt',
            order = 'desc'
        } = req.query;

        // Mock contractor data - replace with actual API call to user-management-service
        const mockContractors = {
            contractors: [
                {
                    _id: '507f1f77bcf86cd799439016',
                    user: {
                        _id: '507f1f77bcf86cd799439017',
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        phone: '+91-9876543213'
                    },
                    experience: 8,
                    serviceAreas: ['Bangalore', 'Mysore'],
                    specialties: ['Construction', 'Renovation'],
                    verificationStatus: 'verified',
                    ratings: 4.7,
                    totalProjects: 45,
                    completionRate: 95.5,
                    createdAt: '2024-01-10T08:30:00Z',
                    approvalDate: '2024-01-12T16:45:00Z'
                },
                {
                    _id: '507f1f77bcf86cd799439018',
                    user: {
                        _id: '507f1f77bcf86cd799439019',
                        name: 'Sarah Davis',
                        email: '<EMAIL>',
                        phone: '+91-9876543214'
                    },
                    experience: 3,
                    serviceAreas: ['Chennai', 'Madurai'],
                    specialties: ['Electrical', 'Plumbing'],
                    verificationStatus: 'pending',
                    ratings: 0,
                    totalProjects: 0,
                    completionRate: 0,
                    createdAt: '2024-01-19T10:15:00Z'
                }
            ],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 6,
                totalContractors: 120,
                hasNext: true,
                hasPrev: false
            }
        };

        res.status(200).json({
            success: true,
            data: mockContractors.contractors,
            pagination: mockContractors.pagination
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch contractors',
            error: error.message
        });
    }
};

// Get contractor applications
exports.getContractorApplications = async (req, res) => {
    try {
        const { status = 'pending' } = req.query;

        const applications = await VerificationRequests.find({
            type: 'contractor',
            status: status
        })
        .populate('requesterId', 'name email phone')
        .sort({ createdAt: -1 });

        // Mock additional application details
        const enrichedApplications = applications.map(app => ({
            ...app.toObject(),
            applicationDetails: {
                experience: 5,
                serviceAreas: ['Delhi', 'Gurgaon'],
                specialties: ['Construction', 'Interior'],
                portfolio: [
                    { image: 'https://example.com/project1.jpg', caption: 'Residential project' },
                    { image: 'https://example.com/project2.jpg', caption: 'Commercial building' }
                ],
                documents: [
                    { type: 'license', url: 'https://example.com/contractor-license.pdf', verified: false },
                    { type: 'insurance', url: 'https://example.com/insurance.pdf', verified: false }
                ]
            }
        }));

        res.status(200).json({
            success: true,
            data: enrichedApplications,
            count: enrichedApplications.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch contractor applications',
            error: error.message
        });
    }
};

// Approve contractor application
exports.approveContractorApplication = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { notes = '' } = req.body;
        const adminId = req.user.id;

        // Find and update verification request
        const application = await VerificationRequests.findById(applicationId);
        if (!application) {
            return res.status(404).json({
                success: false,
                message: 'Application not found'
            });
        }

        if (application.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Application has already been processed'
            });
        }

        // Update verification request
        application.status = 'approved';
        application.varifiedBy = adminId;
        await application.save();

        // Mock API call to user-management-service to update contractor status
        const approvalResult = {
            applicationId,
            contractorId: application.requesterId,
            status: 'approved',
            approvedBy: adminId,
            approvedAt: new Date(),
            notes
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'contractor_approval',
            targetType: 'Contractor',
            targetId: application.requesterId,
            details: { applicationId, notes }
        });

        res.status(200).json({
            success: true,
            message: 'Contractor application approved successfully',
            data: approvalResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to approve contractor application',
            error: error.message
        });
    }
};

// Reject contractor application
exports.rejectContractorApplication = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { reason } = req.body;
        const adminId = req.user.id;

        if (!reason) {
            return res.status(400).json({
                success: false,
                message: 'Reason for rejection is required'
            });
        }

        // Find and update verification request
        const application = await VerificationRequests.findById(applicationId);
        if (!application) {
            return res.status(404).json({
                success: false,
                message: 'Application not found'
            });
        }

        if (application.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Application has already been processed'
            });
        }

        // Update verification request
        application.status = 'rejected';
        application.varifiedBy = adminId;
        application.reasonForRejection = reason;
        await application.save();

        // Mock API call to user-management-service to update contractor status
        const rejectionResult = {
            applicationId,
            contractorId: application.requesterId,
            status: 'rejected',
            rejectedBy: adminId,
            rejectedAt: new Date(),
            reason
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'contractor_rejection',
            targetType: 'Contractor',
            targetId: application.requesterId,
            details: { applicationId, reason }
        });

        res.status(200).json({
            success: true,
            message: 'Contractor application rejected',
            data: rejectionResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to reject contractor application',
            error: error.message
        });
    }
};

// Update contractor rating
exports.updateContractorRating = async (req, res) => {
    try {
        const { contractorId } = req.params;
        const { rating, reason } = req.body;
        const adminId = req.user.id;

        if (!rating || rating < 1 || rating > 5) {
            return res.status(400).json({
                success: false,
                message: 'Rating must be between 1 and 5'
            });
        }

        // Mock API call to user-management-service to update rating
        const updateResult = {
            contractorId,
            rating,
            reason,
            updatedBy: adminId,
            updatedAt: new Date()
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'contractor_rating_update',
            targetType: 'Contractor',
            targetId: contractorId,
            details: { newRating: rating, reason }
        });

        res.status(200).json({
            success: true,
            message: 'Contractor rating updated successfully',
            data: updateResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update contractor rating',
            error: error.message
        });
    }
};
