const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const Report = require('../../model/report');
const Admin = require('../../model/admin');

// Get audit logs with pagination and filters
exports.getAuditLogs = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            adminId,
            action,
            targetType,
            dateFrom,
            dateTo,
            sortBy = 'createdAt',
            order = 'desc'
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const filter = {};

        // Apply filters
        if (adminId) filter.adminId = adminId;
        if (action) filter.action = action;
        if (targetType) filter.targetType = targetType;
        if (dateFrom || dateTo) {
            filter.createdAt = {};
            if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
            if (dateTo) filter.createdAt.$lte = new Date(dateTo);
        }

        const auditLogs = await AuditLog.find(filter)
            .populate('adminId', 'name email')
            .sort({ [sortBy]: order === 'desc' ? -1 : 1 })
            .skip(skip)
            .limit(parseInt(limit));

        const totalLogs = await AuditLog.countDocuments(filter);

        res.status(200).json({
            success: true,
            data: auditLogs,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalLogs / parseInt(limit)),
                totalLogs,
                hasNext: skip + parseInt(limit) < totalLogs,
                hasPrev: parseInt(page) > 1
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch audit logs',
            error: error.message
        });
    }
};

// Export audit logs
exports.exportAuditLogs = async (req, res) => {
    try {
        const {
            adminId,
            action,
            targetType,
            dateFrom,
            dateTo,
            format = 'csv'
        } = req.query;

        const filter = {};
        if (adminId) filter.adminId = adminId;
        if (action) filter.action = action;
        if (targetType) filter.targetType = targetType;
        if (dateFrom || dateTo) {
            filter.createdAt = {};
            if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
            if (dateTo) filter.createdAt.$lte = new Date(dateTo);
        }

        // Mock export - in real implementation, generate actual file
        const exportResult = {
            fileName: `audit_logs_${Date.now()}.${format}`,
            fileUrl: `https://example.com/exports/audit_logs_${Date.now()}.${format}`,
            recordCount: 1250,
            generatedAt: new Date()
        };

        res.status(200).json({
            success: true,
            message: 'Audit logs export generated successfully',
            data: exportResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to export audit logs',
            error: error.message
        });
    }
};

// Generate report
exports.generateReport = async (req, res) => {
    try {
        const { reportType, parameters = {} } = req.body;
        const adminId = req.user.id;

        const validReportTypes = [
            'user_activity',
            'revenue',
            'transactions',
            'land_listings',
            'broker_performance',
            'contractor_performance'
        ];

        if (!validReportTypes.includes(reportType)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid report type'
            });
        }

        // Create report record
        const report = new Report({
            name: `${reportType.replace('_', ' ').toUpperCase()} Report`,
            type: reportType,
            parameters,
            generatedBy: adminId,
            status: 'generating'
        });

        await report.save();

        // Mock report generation - in real implementation, trigger background job
        setTimeout(async () => {
            report.status = 'completed';
            report.fileUrl = `https://example.com/reports/${report._id}.pdf`;
            report.fileSize = 2048576; // 2MB
            report.recordCount = 1500;
            report.completedAt = new Date();
            await report.save();
        }, 5000);

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'report_generated',
            targetType: 'System',
            targetId: report._id,
            details: { reportType, parameters }
        });

        res.status(202).json({
            success: true,
            message: 'Report generation started',
            data: {
                reportId: report._id,
                status: report.status,
                estimatedTime: '2-5 minutes'
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to generate report',
            error: error.message
        });
    }
};

// Get report history
exports.getReportHistory = async (req, res) => {
    try {
        const adminId = req.user.id;

        const reports = await Report.find({ generatedBy: adminId })
            .populate('generatedBy', 'name email')
            .sort({ createdAt: -1 })
            .limit(50);

        res.status(200).json({
            success: true,
            data: reports
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch report history',
            error: error.message
        });
    }
};

// Download report
exports.downloadReport = async (req, res) => {
    try {
        const { reportId } = req.params;

        const report = await Report.findById(reportId);
        if (!report) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        if (report.status !== 'completed') {
            return res.status(400).json({
                success: false,
                message: 'Report is not ready for download'
            });
        }

        // Mock download - in real implementation, stream the file
        res.status(200).json({
            success: true,
            message: 'Report download initiated',
            data: {
                downloadUrl: report.fileUrl,
                fileName: `${report.name}_${report._id}.pdf`,
                fileSize: report.fileSize
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to download report',
            error: error.message
        });
    }
};

// Get admin users
exports.getAdminUsers = async (req, res) => {
    try {
        const admins = await Admin.find({ isActive: true })
            .select('-password')
            .populate('createdBy', 'name email')
            .sort({ createdAt: -1 });

        res.status(200).json({
            success: true,
            data: admins
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch admin users',
            error: error.message
        });
    }
};

// Create admin user
exports.createAdminUser = async (req, res) => {
    try {
        const adminData = req.body;
        const creatorId = req.user.id;

        const admin = new Admin({
            ...adminData,
            createdBy: creatorId
        });

        await admin.save();

        // Remove password from response
        const adminResponse = admin.toObject();
        delete adminResponse.password;

        // Log the action
        await AuditLog.create({
            adminId: creatorId,
            action: 'admin_user_creation',
            targetType: 'Admin',
            targetId: admin._id,
            details: { adminEmail: admin.email, role: admin.role }
        });

        res.status(201).json({
            success: true,
            message: 'Admin user created successfully',
            data: adminResponse
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to create admin user',
            error: error.message
        });
    }
};

// Update admin user
exports.updateAdminUser = async (req, res) => {
    try {
        const { adminId } = req.params;
        const updateData = req.body;
        const updaterId = req.user.id;

        // Remove password from update data if present
        delete updateData.password;

        const admin = await Admin.findByIdAndUpdate(
            adminId,
            updateData,
            { new: true }
        ).select('-password');

        if (!admin) {
            return res.status(404).json({
                success: false,
                message: 'Admin user not found'
            });
        }

        // Log the action
        await AuditLog.create({
            adminId: updaterId,
            action: 'admin_user_update',
            targetType: 'Admin',
            targetId: adminId,
            details: { updatedFields: Object.keys(updateData) }
        });

        res.status(200).json({
            success: true,
            message: 'Admin user updated successfully',
            data: admin
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update admin user',
            error: error.message
        });
    }
};

// Update admin permissions
exports.updateAdminPermissions = async (req, res) => {
    try {
        const { adminId } = req.params;
        const { permissions } = req.body;
        const updaterId = req.user.id;

        if (!Array.isArray(permissions)) {
            return res.status(400).json({
                success: false,
                message: 'Permissions must be an array'
            });
        }

        const admin = await Admin.findByIdAndUpdate(
            adminId,
            { permissions },
            { new: true }
        ).select('-password');

        if (!admin) {
            return res.status(404).json({
                success: false,
                message: 'Admin user not found'
            });
        }

        // Log the action
        await AuditLog.create({
            adminId: updaterId,
            action: 'admin_permission_update',
            targetType: 'Admin',
            targetId: adminId,
            details: { newPermissions: permissions }
        });

        res.status(200).json({
            success: true,
            message: 'Admin permissions updated successfully',
            data: admin
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update admin permissions',
            error: error.message
        });
    }
};

// Deactivate admin user
exports.deactivateAdminUser = async (req, res) => {
    try {
        const { adminId } = req.params;
        const deactivatorId = req.user.id;

        const admin = await Admin.findByIdAndUpdate(
            adminId,
            { isActive: false },
            { new: true }
        ).select('-password');

        if (!admin) {
            return res.status(404).json({
                success: false,
                message: 'Admin user not found'
            });
        }

        // Log the action
        await AuditLog.create({
            adminId: deactivatorId,
            action: 'admin_user_deactivation',
            targetType: 'Admin',
            targetId: adminId,
            details: { deactivatedAdmin: admin.email }
        });

        res.status(200).json({
            success: true,
            message: 'Admin user deactivated successfully',
            data: admin
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to deactivate admin user',
            error: error.message
        });
    }
};
