const mongoose = require('mongoose');

const auditLogSchema = new mongoose.Schema(
    {
        adminId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
            required: true,
        },
        action: {
            type: String,
            required: true,
            enum: [
                'user_status_update',
                'user_verification',
                'user_deletion',
                'broker_approval',
                'broker_rejection',
                'contractor_approval',
                'contractor_rejection',
                'land_verification',
                'land_rejection',
                'land_flagging',
                'transaction_refund',
                'transaction_dispute',
                'ticket_status_update',
                'ticket_assignment',
                'content_moderation',
                'content_ban',
                'system_settings_update',
                'admin_user_creation',
                'admin_user_update',
                'admin_permission_update',
                'notification_sent',
                'report_generated'
            ]
        },
        targetType: {
            type: String,
            required: true,
            enum: ['User', 'Broker', 'Contractor', 'Site', 'Transaction', 'Ticket', 'Content', 'Admin', 'System']
        },
        targetId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        details: {
            type: mongoose.Schema.Types.Mixed,
            default: {}
        },
        ipAddress: {
            type: String,
        },
        userAgent: {
            type: String,
        },
        previousState: {
            type: mongoose.Schema.Types.Mixed,
        },
        newState: {
            type: mongoose.Schema.Types.Mixed,
        },
        reason: {
            type: String,
        }
    },
    {
        timestamps: true,
    }
);

// Index for efficient querying
auditLogSchema.index({ adminId: 1, createdAt: -1 });
auditLogSchema.index({ action: 1, createdAt: -1 });
auditLogSchema.index({ targetType: 1, targetId: 1 });

const AuditLog = mongoose.model('AuditLog', auditLogSchema);
module.exports = AuditLog;
