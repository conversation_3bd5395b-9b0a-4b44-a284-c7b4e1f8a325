const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const VerificationRequests = require('../../model/verificationRequests');
const httpClient = require('../../utils/httpClient');

// Get all brokers with pagination and filters
exports.getAllBrokers = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            verificationStatus,
            search,
            sortBy = 'createdAt',
            order = 'desc',
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Mock broker data - replace with actual API call to user-management-service
        const mockBrokers = {
            brokers: [
                {
                    _id: '507f1f77bcf86cd799439013',
                    user: {
                        _id: '507f1f77bcf86cd799439012',
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        phone: '+91-**********',
                    },
                    licenseNumber: 'BRK-2024-001',
                    experience: 5,
                    serviceAreas: ['Bangalore', 'Mysore'],
                    specialties: ['Residential', 'Commercial'],
                    verificationStatus: 'verified',
                    ratings: 4.5,
                    totalDeals: 23,
                    commissionRate: 2.5,
                    createdAt: '2024-01-14T09:15:00Z',
                    approvalDate: '2024-01-16T14:30:00Z',
                },
                {
                    _id: '507f1f77bcf86cd799439014',
                    user: {
                        _id: '507f1f77bcf86cd799439015',
                        name: 'Mike Johnson',
                        email: '<EMAIL>',
                        phone: '+91-**********',
                    },
                    licenseNumber: 'BRK-2024-002',
                    experience: 3,
                    serviceAreas: ['Chennai', 'Coimbatore'],
                    specialties: ['Agricultural', 'Industrial'],
                    verificationStatus: 'pending',
                    ratings: 0,
                    totalDeals: 0,
                    commissionRate: 2.0,
                    createdAt: '2024-01-18T11:20:00Z',
                },
            ],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 5,
                totalBrokers: 85,
                hasNext: true,
                hasPrev: false,
            },
        };

        res.status(200).json({
            success: true,
            data: mockBrokers.brokers,
            pagination: mockBrokers.pagination,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch brokers',
            error: error.message,
        });
    }
};

// Get broker applications
exports.getBrokerApplications = async (req, res) => {
    try {
        const { status = 'pending' } = req.query;

        const applications = await VerificationRequests.find({
            type: 'broker',
            status: status,
        })
            .populate('requesterId', 'name email phone')
            .sort({ createdAt: -1 });

        // Mock additional application details
        const enrichedApplications = applications.map((app) => ({
            ...app.toObject(),
            applicationDetails: {
                licenseNumber: 'BRK-2024-003',
                experience: 2,
                serviceAreas: ['Mumbai', 'Pune'],
                specialties: ['Residential'],
                documents: [
                    {
                        type: 'license',
                        url: 'https://example.com/license.pdf',
                        verified: false,
                    },
                    {
                        type: 'identity',
                        url: 'https://example.com/id.pdf',
                        verified: false,
                    },
                ],
            },
        }));

        res.status(200).json({
            success: true,
            data: enrichedApplications,
            count: enrichedApplications.length,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch broker applications',
            error: error.message,
        });
    }
};

// Approve broker application
exports.approveBrokerApplication = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { commissionRate = 2.0, notes = '' } = req.body;
        const adminId = req.user.id;

        // Find and update verification request
        const application = await VerificationRequests.findById(applicationId);
        if (!application) {
            return res.status(404).json({
                success: false,
                message: 'Application not found',
            });
        }

        if (application.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Application has already been processed',
            });
        }

        // Update verification request
        application.status = 'approved';
        application.varifiedBy = adminId;
        await application.save();

        // Mock API call to user-management-service to update broker status
        const approvalResult = {
            applicationId,
            brokerId: application.requesterId,
            status: 'approved',
            commissionRate,
            approvedBy: adminId,
            approvedAt: new Date(),
            notes,
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'broker_approval',
            targetType: 'Broker',
            targetId: application.requesterId,
            details: { applicationId, commissionRate, notes },
        });

        res.status(200).json({
            success: true,
            message: 'Broker application approved successfully',
            data: approvalResult,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to approve broker application',
            error: error.message,
        });
    }
};

// Reject broker application
exports.rejectBrokerApplication = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { reason } = req.body;
        const adminId = req.user.id;

        if (!reason) {
            return res.status(400).json({
                success: false,
                message: 'Reason for rejection is required',
            });
        }

        // Find and update verification request
        const application = await VerificationRequests.findById(applicationId);
        if (!application) {
            return res.status(404).json({
                success: false,
                message: 'Application not found',
            });
        }

        if (application.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Application has already been processed',
            });
        }

        // Update verification request
        application.status = 'rejected';
        application.varifiedBy = adminId;
        application.reasonForRejection = reason;
        await application.save();

        // Mock API call to user-management-service to update broker status
        const rejectionResult = {
            applicationId,
            brokerId: application.requesterId,
            status: 'rejected',
            rejectedBy: adminId,
            rejectedAt: new Date(),
            reason,
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'broker_rejection',
            targetType: 'Broker',
            targetId: application.requesterId,
            details: { applicationId, reason },
        });

        res.status(200).json({
            success: true,
            message: 'Broker application rejected',
            data: rejectionResult,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to reject broker application',
            error: error.message,
        });
    }
};

// Update broker commission
exports.updateBrokerCommission = async (req, res) => {
    try {
        const { brokerId } = req.params;
        const { commissionRate } = req.body;
        const adminId = req.user.id;

        if (!commissionRate || commissionRate < 0 || commissionRate > 10) {
            return res.status(400).json({
                success: false,
                message: 'Commission rate must be between 0 and 10 percent',
            });
        }

        // Mock API call to user-management-service to update commission
        const updateResult = {
            brokerId,
            commissionRate,
            updatedBy: adminId,
            updatedAt: new Date(),
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'broker_commission_update',
            targetType: 'Broker',
            targetId: brokerId,
            details: { newCommissionRate: commissionRate },
        });

        res.status(200).json({
            success: true,
            message: 'Broker commission updated successfully',
            data: updateResult,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update broker commission',
            error: error.message,
        });
    }
};
