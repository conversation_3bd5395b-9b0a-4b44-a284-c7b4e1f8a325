const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');

// Get reported content
exports.getReportedContent = async (req, res) => {
    try {
        const { contentType = 'all' } = req.query;

        // Mock reported content data
        const reportedContent = [
            {
                _id: '507f1f77bcf86cd799439032',
                contentId: '507f1f77bcf86cd799439020',
                contentType: 'lands',
                title: 'Suspicious Land Listing',
                description: 'This listing seems to have fake documents',
                reportedBy: {
                    _id: '507f1f77bcf86cd799439011',
                    name: '<PERSON>',
                    email: '<EMAIL>'
                },
                reportReason: 'Fraudulent documents',
                status: 'pending',
                reportedAt: '2024-01-22T10:30:00Z',
                content: {
                    title: 'Prime Agricultural Land',
                    owner: '<PERSON><PERSON>',
                    location: 'Bangalore Rural'
                }
            },
            {
                _id: '507f1f77bcf86cd799439033',
                contentId: '507f1f77bcf86cd799439034',
                contentType: 'reviews',
                title: 'Inappropriate Review',
                description: 'Contains offensive language',
                reportedBy: {
                    _id: '507f1f77bcf86cd799439012',
                    name: 'Jane Smith',
                    email: '<EMAIL>'
                },
                reportReason: 'Inappropriate language',
                status: 'pending',
                reportedAt: '2024-01-21T15:45:00Z',
                content: {
                    reviewText: 'This broker is terrible...',
                    reviewer: 'Anonymous User',
                    rating: 1
                }
            }
        ];

        // Filter by content type if specified
        const filteredContent = contentType === 'all' 
            ? reportedContent 
            : reportedContent.filter(item => item.contentType === contentType);

        res.status(200).json({
            success: true,
            data: filteredContent,
            count: filteredContent.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch reported content',
            error: error.message
        });
    }
};

// Moderate content
exports.moderateContent = async (req, res) => {
    try {
        const { contentId, contentType, action, reason = '' } = req.body;
        const adminId = req.user.id;

        const validActions = ['approve', 'remove', 'flag'];
        if (!validActions.includes(action)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid action. Must be one of: approve, remove, flag'
            });
        }

        // Mock moderation action
        const moderationResult = {
            contentId,
            contentType,
            action,
            reason,
            moderatedBy: adminId,
            moderatedAt: new Date()
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'content_moderation',
            targetType: 'Content',
            targetId: contentId,
            details: { contentType, action, reason }
        });

        res.status(200).json({
            success: true,
            message: `Content ${action}d successfully`,
            data: moderationResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to moderate content',
            error: error.message
        });
    }
};

// Ban content
exports.banContent = async (req, res) => {
    try {
        const { contentId, contentType, reason } = req.body;
        const adminId = req.user.id;

        if (!reason) {
            return res.status(400).json({
                success: false,
                message: 'Reason for banning is required'
            });
        }

        // Mock ban action
        const banResult = {
            contentId,
            contentType,
            status: 'banned',
            reason,
            bannedBy: adminId,
            bannedAt: new Date()
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'content_ban',
            targetType: 'Content',
            targetId: contentId,
            details: { contentType, reason }
        });

        res.status(200).json({
            success: true,
            message: 'Content banned successfully',
            data: banResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to ban content',
            error: error.message
        });
    }
};
