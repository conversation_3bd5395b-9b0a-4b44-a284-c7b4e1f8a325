const ExpressError = require('@build-connect/utils/ExpressError');
const Project = require('../../model/project');

/**
 * Add progress log to a project (Contractor only)
 */
exports.addProgressLog = async (req, res) => {
    const { projectId } = req.params;
    const { stage, description } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Check if user is a contractor
    if (userRole !== 'contractor') {
        throw new ExpressError('Only contractors can add progress logs', 403);
    }

    // Validate input
    if (!stage || stage.trim().length === 0) {
        throw new ExpressError('Stage is required', 400);
    }

    // Find the project and verify contractor is assigned
    const project = await Project.findById(projectId);
    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    // Check if the contractor is assigned to this project
    if (!project.contractorId || project.contractorId.toString() !== userId) {
        throw new ExpressError('You are not assigned to this project', 403);
    }

    // Add progress log
    const progressLog = {
        stage,
        description,
        addedById: userId,
        addedByRole: 'Contractor',
        date: new Date()
    };

    project.progressLogs.push(progressLog);
    await project.save();

    // Populate the added progress log with user details
    const updatedProject = await Project.findById(projectId)
        .populate('progressLogs.addedById', 'name email')
        .lean();

    const addedLog = updatedProject.progressLogs[updatedProject.progressLogs.length - 1];

    res.status(201).json({
        message: 'Progress log added successfully',
        progressLog: addedLog
    });
};

/**
 * Get progress logs for a project
 */
exports.getProgressLogs = async (req, res) => {
    const { projectId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Find the project
    const project = await Project.findById(projectId)
        .populate('progressLogs.addedById', 'name email')
        .lean();

    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    // Check access permissions
    const hasAccess = 
        project.userId.toString() === userId || // Project owner
        (project.contractorId && project.contractorId.toString() === userId) || // Assigned contractor
        (project.brokerId && project.brokerId.toString() === userId); // Assigned broker

    if (!hasAccess) {
        throw new ExpressError('You do not have access to this project', 403);
    }

    res.status(200).json({
        projectId,
        progressLogs: project.progressLogs || []
    });
};

/**
 * Update a progress log (Contractor only)
 */
exports.updateProgressLog = async (req, res) => {
    const { projectId, logId } = req.params;
    const { stage, description } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Check if user is a contractor
    if (userRole !== 'contractor') {
        throw new ExpressError('Only contractors can update progress logs', 403);
    }

    // Find the project
    const project = await Project.findById(projectId);
    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    // Check if the contractor is assigned to this project
    if (!project.contractorId || project.contractorId.toString() !== userId) {
        throw new ExpressError('You are not assigned to this project', 403);
    }

    // Find the progress log
    const progressLog = project.progressLogs.id(logId);
    if (!progressLog) {
        throw new ExpressError('Progress log not found', 404);
    }

    // Check if the contractor owns this log
    if (progressLog.addedById.toString() !== userId) {
        throw new ExpressError('You can only update your own progress logs', 403);
    }

    // Update the progress log
    progressLog.stage = stage || progressLog.stage;
    progressLog.description = description || progressLog.description;
    progressLog.date = new Date(); // Update timestamp

    await project.save();

    // Return updated log with user details
    const updatedProject = await Project.findById(projectId)
        .populate('progressLogs.addedById', 'name email')
        .lean();

    const updatedLog = updatedProject.progressLogs.find(log => log._id.toString() === logId);

    res.status(200).json({
        message: 'Progress log updated successfully',
        progressLog: updatedLog
    });
};

/**
 * Delete a progress log (Contractor only)
 */
exports.deleteProgressLog = async (req, res) => {
    const { projectId, logId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Check if user is a contractor
    if (userRole !== 'contractor') {
        throw new ExpressError('Only contractors can delete progress logs', 403);
    }

    // Find the project
    const project = await Project.findById(projectId);
    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    // Check if the contractor is assigned to this project
    if (!project.contractorId || project.contractorId.toString() !== userId) {
        throw new ExpressError('You are not assigned to this project', 403);
    }

    // Find the progress log
    const progressLog = project.progressLogs.id(logId);
    if (!progressLog) {
        throw new ExpressError('Progress log not found', 404);
    }

    // Check if the contractor owns this log
    if (progressLog.addedById.toString() !== userId) {
        throw new ExpressError('You can only delete your own progress logs', 403);
    }

    // Remove the progress log
    project.progressLogs.pull(logId);
    await project.save();

    res.status(200).json({
        message: 'Progress log deleted successfully'
    });
};
