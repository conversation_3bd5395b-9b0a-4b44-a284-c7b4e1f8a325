const mongoose = require('mongoose');

const systemSettingsSchema = new mongoose.Schema(
    {
        key: {
            type: String,
            required: true,
            unique: true,
        },
        value: {
            type: mongoose.Schema.Types.Mixed,
            required: true,
        },
        description: {
            type: String,
        },
        category: {
            type: String,
            enum: [
                'general',
                'commission',
                'verification',
                'notification',
                'security',
                'payment',
            ],
            default: 'general',
        },
        isEditable: {
            type: Boolean,
            default: true,
        },
        lastModifiedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
        },
    },
    {
        timestamps: true,
    }
);

const SystemSettings = mongoose.model('SystemSettings', systemSettingsSchema);
module.exports = SystemSettings;
