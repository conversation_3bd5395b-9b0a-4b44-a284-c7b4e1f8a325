{"info": {"name": "Complete Admin Management Service API", "description": "Comprehensive API testing collection for Admin Management Service - All 30 endpoints with proper payloads and validation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001/admin-service/api/v1", "type": "string"}, {"key": "jwt_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1NDMyMzUsImV4cCI6MTc1NDU3OTIzNX0.12Uldfibk3BL1RpB1tKtJxG0LHgPwMl03auP0XC9bhQ", "type": "string"}, {"key": "session_id", "value": "tnnijVibRuu7iNYCeVyFVKveEzWDWxqEESTOBc4twL0", "type": "string"}, {"key": "verification_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "user_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "site_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "transaction_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "rating_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "ticket_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "admin_id", "value": "689421a8575779a37874d557", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Collection-level pre-request script", "if (!pm.environment.get('jwt_token') && !pm.collectionVariables.get('jwt_token')) {", "    console.log('Warning: JWT token not set. Please update jwt_token variable.');", "}", "if (!pm.environment.get('session_id') && !pm.collectionVariables.get('session_id')) {", "    console.log('Warning: Session ID not set. Please update session_id variable.');", "}"]}}], "item": [{"name": "🏠 Dashboard & Analytics", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has dashboard data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalVerificationRequests');", "    pm.expect(jsonData).to.have.property('pendingRequests');", "});"]}}]}, {"name": "Get Dashboard Analytics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/analytics", "host": ["{{baseUrl}}"], "path": ["analytics"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has analytics data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('object');", "});"]}}]}]}, {"name": "🔍 Verification Management", "item": [{"name": "Get Verification Requests", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications?page=1&limit=10&status=pending", "host": ["{{baseUrl}}"], "path": ["verifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "pending"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has verification requests', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('verificationRequests');", "    pm.expect(jsonData.verificationRequests).to.be.an('array');", "    ", "    // Store first verification ID if available", "    if (jsonData.verificationRequests.length > 0) {", "        pm.collectionVariables.set('verification_id', jsonData.verificationRequests[0]._id);", "    }", "});"]}}]}, {"name": "Get Verification Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/stats?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["verifications", "stats"], "query": [{"key": "timeframe", "value": "30d"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has statistics', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('stats');", "});"]}}]}, {"name": "Get Single Verification Request", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});"]}}]}, {"name": "Approve Verification Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reasonForApproval\": \"All documents verified successfully\",\n  \"notes\": \"Verification completed by admin team\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/approve", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "approve"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}, {"name": "Reject Verification Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reasonForRejection\": \"Missing required documents\",\n  \"notes\": \"Please resubmit with all required documentation\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/reject", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "reject"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}, {"name": "Update Verification Priority", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/priority", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "priority"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}, {"name": "Add Verification Notes", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": \"Additional verification required for business license\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/notes", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "notes"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}]}, {"name": "👥 User Management", "item": [{"name": "Get Users List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/users?page=1&limit=10&role=contractor", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "role", "value": "contractor"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has users array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('users');", "    pm.expect(jsonData.users).to.be.an('array');", "});"]}}]}, {"name": "Get Contractors List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/contractors?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["contractors"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has contractors array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('contractors');", "});"]}}]}, {"name": "Get User Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/users/stats?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["users", "stats"], "query": [{"key": "timeframe", "value": "30d"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has user statistics', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('stats');", "});"]}}]}, {"name": "Get Single User", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/users/{{user_id}}", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});"]}}]}, {"name": "Update User Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": false,\n  \"reason\": \"Policy violation - spam reports\"\n}"}, "url": {"raw": "{{baseUrl}}/users/{{user_id}}/status", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}", "status"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}, {"name": "Get Brokers List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/brokers?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["brokers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has brokers array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('brokers');", "});"]}}]}]}, {"name": "🏗️ Site Management", "item": [{"name": "Get Sites List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/sites?page=1&limit=10&state=Karnataka", "host": ["{{baseUrl}}"], "path": ["sites"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "state", "value": "Karnataka"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sites array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('sites');", "    pm.expect(jsonData.sites).to.be.an('array');", "    ", "    // Store first site ID if available", "    if (jsonData.sites.length > 0) {", "        pm.collectionVariables.set('site_id', jsonData.sites[0]._id);", "    }", "});"]}}]}, {"name": "Get Site Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/sites/stats?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["sites", "stats"], "query": [{"key": "timeframe", "value": "30d"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has site statistics', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('stats');", "});"]}}]}, {"name": "Get Sites by Location", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/sites/location?state=Karnataka", "host": ["{{baseUrl}}"], "path": ["sites", "location"], "query": [{"key": "state", "value": "Karnataka"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sites by location', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('object');", "});"]}}]}, {"name": "Get Single Site", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/sites/{{site_id}}", "host": ["{{baseUrl}}"], "path": ["sites", "{{site_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});"]}}]}, {"name": "Update Site Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"reason\": \"Site meets all safety and regulatory requirements\"\n}"}, "url": {"raw": "{{baseUrl}}/sites/{{site_id}}/status", "host": ["{{baseUrl}}"], "path": ["sites", "{{site_id}}", "status"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}, {"name": "Get Project Requirements", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/projects?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["projects"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has projects array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('projects');", "});"]}}]}, {"name": "Get Service Requests", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/service-requests?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["service-requests"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has service requests array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('serviceRequests');", "});"]}}]}]}, {"name": "💰 Service Management", "item": [{"name": "Get Transactions", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/transactions", "host": ["{{baseUrl}}"], "path": ["transactions"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has transactions array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('transactions');", "    pm.expect(jsonData.transactions).to.be.an('array');", "    ", "    // Store first transaction ID if available", "    if (jsonData.transactions.length > 0) {", "        pm.collectionVariables.set('transaction_id', jsonData.transactions[0]._id);", "    }", "});"]}}]}, {"name": "Get Single Transaction", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/transactions/{{transaction_id}}", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transaction_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});"]}}]}, {"name": "Get Ratings", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/ratings", "host": ["{{baseUrl}}"], "path": ["ratings"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has ratings array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('ratings');", "    pm.expect(jsonData.ratings).to.be.an('array');", "    ", "    // Store first rating ID if available", "    if (jsonData.ratings.length > 0) {", "        pm.collectionVariables.set('rating_id', jsonData.ratings[0]._id);", "    }", "});"]}}]}, {"name": "Delete Rating", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/ratings/{{rating_id}}", "host": ["{{baseUrl}}"], "path": ["ratings", "{{rating_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 404, or 403', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404, 403]);", "});"]}}]}]}, {"name": "📢 Notification Management", "item": [{"name": "Get Notifications", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/notifications?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["notifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has notifications array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('notifications');", "    pm.expect(jsonData.notifications).to.be.an('array');", "});"]}}]}, {"name": "Broadcast Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"System Maintenance Notice\",\n  \"message\": \"Scheduled maintenance will occur on Sunday from 2 AM to 4 AM IST. Services may be temporarily unavailable.\",\n  \"type\": \"maintenance\",\n  \"targetAudience\": \"all\"\n}"}, "url": {"raw": "{{baseUrl}}/notifications/broadcast", "host": ["{{baseUrl}}"], "path": ["notifications", "broadcast"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 400', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400]);", "});"]}}]}]}, {"name": "🎫 Support Ticket Management", "item": [{"name": "Get Support Tickets", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/support/tickets?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["support", "tickets"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has support tickets array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('tickets');", "    pm.expect(jsonData.tickets).to.be.an('array');", "    ", "    // Store first ticket ID if available", "    if (jsonData.tickets.length > 0) {", "        pm.collectionVariables.set('ticket_id', jsonData.tickets[0]._id);", "    }", "});"]}}]}, {"name": "Update Support Ticket Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_progress\",\n  \"assignedTo\": \"{{admin_id}}\",\n  \"notes\": \"Ticket assigned to technical team for investigation\"\n}"}, "url": {"raw": "{{baseUrl}}/support/tickets/{{ticket_id}}/status", "host": ["{{baseUrl}}"], "path": ["support", "tickets", "{{ticket_id}}", "status"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}]}]}