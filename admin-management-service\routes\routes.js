const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createFixedAuthMiddleware = require('../middleware/auth-fix');
const {
    handleValidationErrors,
} = require('@build-connect/utils/middleware/expressValidator');
const { getTickets } = require('../controllers/getadmins');

// Import validators
const {
    verificationListValidators,
    verificationApprovalValidators,
    verificationRejectionValidators,
    verificationPriorityValidators,
    verificationNotesValidators,
    userListValidators,
    userStatusValidators,
    siteListValidators,
    siteStatusValidators,
    broadcastNotificationValidators,
    supportTicketStatusValidators,
    validators,
} = require('../utils/validators');

// Import all controllers
const {
    getVerificationRequests,
    getVerificationRequest,
} = require('../controllers/verification/list');
const {
    approveVerificationRequest,
    rejectVerificationRequest,
    updateVerificationPriority,
    addVerificationNotes,
    getVerificationStats,
} = require('../controllers/verification/manage');
const {
    getAllUsers,
    getUserById,
    updateUserStatus,
    getUserStats,
    getContractors,
    getBrokers,
} = require('../controllers/users/manage');
const {
    getAllSites,
    getSiteById,
    updateSiteStatus,
    getSiteStats,
    getSitesByLocation,
    getProjectRequirements,
    getServiceRequests,
} = require('../controllers/sites/manage');
const { getDashboardAnalytics } = require('../controllers/analytics/dashboard');
const {
    getTransactions,
    getTransactionById,
    getRatings,
    deleteRating,
    getNotifications,
    sendBroadcastNotification,
    getSupportTickets,
    updateSupportTicketStatus,
} = require('../controllers/services/manage');

const { client } = require('../cache');

// Use the fixed authentication middleware instead of the problematic one
const doAuthenticate = createFixedAuthMiddleware(client);

// Dashboard route
router.get('/', doAuthenticate, catchAsync(getTickets));

// Analytics routes
router.get('/analytics', doAuthenticate, catchAsync(getDashboardAnalytics));

// Verification routes
router.get(
    '/verifications',
    verificationListValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getVerificationRequests)
);
router.get(
    '/verifications/stats',
    [validators.timeframe()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getVerificationStats)
);
router.get(
    '/verifications/:id',
    [validators.objectId()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getVerificationRequest)
);
router.patch(
    '/verifications/:id/approve',
    verificationApprovalValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(approveVerificationRequest)
);
router.patch(
    '/verifications/:id/reject',
    verificationRejectionValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(rejectVerificationRequest)
);
router.patch(
    '/verifications/:id/priority',
    verificationPriorityValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(updateVerificationPriority)
);
router.patch(
    '/verifications/:id/notes',
    verificationNotesValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(addVerificationNotes)
);

// User management routes
router.get(
    '/users',
    userListValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getAllUsers)
);
router.get(
    '/users/stats',
    [validators.timeframe()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getUserStats)
);
router.get(
    '/users/:id',
    [validators.objectId()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getUserById)
);
router.patch(
    '/users/:id/status',
    userStatusValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(updateUserStatus)
);
router.get(
    '/contractors',
    userListValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getContractors)
);
router.get(
    '/brokers',
    userListValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getBrokers)
);

// Site management routes
router.get(
    '/sites',
    siteListValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getAllSites)
);
router.get(
    '/sites/stats',
    [validators.timeframe()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getSiteStats)
);
router.get(
    '/sites/location',
    [validators.state()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getSitesByLocation)
);
router.get(
    '/sites/:id',
    [validators.objectId()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getSiteById)
);
router.patch(
    '/sites/:id/status',
    siteStatusValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(updateSiteStatus)
);
router.get(
    '/projects',
    [validators.page(), validators.limit()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getProjectRequirements)
);
router.get(
    '/service-requests',
    [validators.page(), validators.limit()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getServiceRequests)
);

// Transaction management routes
router.get('/transactions', doAuthenticate, catchAsync(getTransactions));
router.get('/transactions/:id', doAuthenticate, catchAsync(getTransactionById));

// Rating management routes
router.get('/ratings', doAuthenticate, catchAsync(getRatings));
router.delete('/ratings/:id', doAuthenticate, catchAsync(deleteRating));

// Notification management routes
router.get(
    '/notifications',
    [validators.page(), validators.limit()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getNotifications)
);
router.post(
    '/notifications/broadcast',
    broadcastNotificationValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(sendBroadcastNotification)
);

// Support ticket management routes
router.get(
    '/support/tickets',
    [validators.page(), validators.limit()],
    handleValidationErrors,
    doAuthenticate,
    catchAsync(getSupportTickets)
);
router.patch(
    '/support/tickets/:id/status',
    supportTicketStatusValidators,
    handleValidationErrors,
    doAuthenticate,
    catchAsync(updateSupportTicketStatus)
);

module.exports = router;
