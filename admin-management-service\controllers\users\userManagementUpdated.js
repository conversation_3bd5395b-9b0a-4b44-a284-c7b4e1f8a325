const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const httpClient = require('../../utils/httpClient');

// Get all users with pagination and filters - UPDATED VERSION
exports.getAllUsers = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            role,
            status,
            isEmailVerified,
            isPhoneVerified,
            search,
            sortBy = 'createdAt',
            order = 'desc',
        } = req.query;

        const authToken = httpClient.getAuthToken(req);
        
        // Build query parameters
        const queryParams = {
            page,
            limit,
            sortBy,
            order
        };

        // Add filters if provided
        if (role) queryParams.role = role;
        if (status) queryParams.status = status;
        if (isEmailVerified !== undefined) queryParams.isEmailVerified = isEmailVerified;
        if (isPhoneVerified !== undefined) queryParams.isPhoneVerified = isPhoneVerified;
        if (search) queryParams.search = search;

        try {
            // Make API call to user-management-service
            const response = await httpClient.getAllUsers(queryParams, authToken);
            
            res.status(200).json({
                success: true,
                data: response.users || response.data || response,
                pagination: response.pagination || {
                    currentPage: parseInt(page),
                    totalPages: 1,
                    totalUsers: Array.isArray(response) ? response.length : 0,
                    hasNext: false,
                    hasPrev: false
                }
            });
        } catch (serviceError) {
            // Handle service unavailable gracefully
            const errorResponse = httpClient.handleServiceError(serviceError, 'User Management');
            res.status(503).json(errorResponse);
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users',
            error: error.message,
        });
    }
};

// Get user details by ID - UPDATED VERSION
exports.getUserDetails = async (req, res) => {
    try {
        const { userId } = req.params;
        const authToken = httpClient.getAuthToken(req);

        try {
            // Make API call to user-management-service
            const response = await httpClient.getUserById(userId, authToken);
            
            res.status(200).json({
                success: true,
                data: response.user || response.data || response
            });
        } catch (serviceError) {
            // Handle service unavailable gracefully
            const errorResponse = httpClient.handleServiceError(serviceError, 'User Management');
            res.status(serviceError.response?.status || 503).json(errorResponse);
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user details',
            error: error.message
        });
    }
};

// Update user status - UPDATED VERSION
exports.updateUserStatus = async (req, res) => {
    try {
        const { userId } = req.params;
        const { status, reason = '' } = req.body;
        const adminId = req.user.id;
        const authToken = httpClient.getAuthToken(req);

        // Validate status
        const validStatuses = ['active', 'suspended', 'banned'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be one of: active, suspended, banned'
            });
        }

        try {
            // Make API call to user-management-service
            const response = await httpClient.updateUserStatus(userId, { status, reason }, authToken);

            // Log the action
            await AuditLog.create({
                adminId,
                action: 'user_status_update',
                targetType: 'User',
                targetId: userId,
                details: { newStatus: status, reason },
                reason
            });

            res.status(200).json({
                success: true,
                message: `User status updated to ${status}`,
                data: response.user || response.data || response
            });
        } catch (serviceError) {
            const errorResponse = httpClient.handleServiceError(serviceError, 'User Management');
            res.status(serviceError.response?.status || 503).json(errorResponse);
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update user status',
            error: error.message
        });
    }
};

// Verify user (email, phone, identity, address) - UPDATED VERSION
exports.verifyUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const { verificationType, status } = req.body;
        const adminId = req.user.id;
        const authToken = httpClient.getAuthToken(req);

        // Validate verification type and status
        const validTypes = ['email', 'phone', 'identity', 'address'];
        const validStatuses = ['verified', 'rejected'];

        if (!validTypes.includes(verificationType)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid verification type'
            });
        }

        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid verification status'
            });
        }

        try {
            // Make API call to user-management-service
            const verificationData = { verificationType, status, verifiedBy: adminId };
            const response = await httpClient.makeRequest('USER_SERVICE', `/users/${userId}/verify`, {
                method: 'PUT',
                data: verificationData,
                headers: { Authorization: authToken }
            });

            // Log the action
            await AuditLog.create({
                adminId,
                action: 'user_verification',
                targetType: 'User',
                targetId: userId,
                details: { verificationType, status }
            });

            res.status(200).json({
                success: true,
                message: `User ${verificationType} verification ${status}`,
                data: response.user || response.data || response
            });
        } catch (serviceError) {
            const errorResponse = httpClient.handleServiceError(serviceError, 'User Management');
            res.status(serviceError.response?.status || 503).json(errorResponse);
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update user verification',
            error: error.message
        });
    }
};

// Delete user - UPDATED VERSION
exports.deleteUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const { reason } = req.body;
        const adminId = req.user.id;
        const authToken = httpClient.getAuthToken(req);

        if (!reason) {
            return res.status(400).json({
                success: false,
                message: 'Reason for deletion is required'
            });
        }

        try {
            // Make API call to user-management-service for soft delete
            const response = await httpClient.makeRequest('USER_SERVICE', `/users/${userId}`, {
                method: 'DELETE',
                data: { reason, deletedBy: adminId },
                headers: { Authorization: authToken }
            });

            // Log the action
            await AuditLog.create({
                adminId,
                action: 'user_deletion',
                targetType: 'User',
                targetId: userId,
                details: { reason }
            });

            res.status(200).json({
                success: true,
                message: 'User deleted successfully',
                data: response.user || response.data || response
            });
        } catch (serviceError) {
            const errorResponse = httpClient.handleServiceError(serviceError, 'User Management');
            res.status(serviceError.response?.status || 503).json(errorResponse);
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to delete user',
            error: error.message
        });
    }
};
