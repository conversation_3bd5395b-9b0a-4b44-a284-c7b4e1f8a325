const ServiceRequest = require('../../model/serviceRequest');
const Project = require('../../model/project');
const ProjectRequirement = require('../../model/projectRequirement');
const User = require('../../../user-management-service/model/user');

exports.getServiceRequests = async (req, res) => {
    const userId = req.user.id;

    const serviceRequests = await ServiceRequest.find({
        recipientId: userId,
    }).lean();

    if (!serviceRequests.length) {
        return res.status(200).json({ serviceRequests: [] });
    }

    const response = await Promise.all(
        serviceRequests.map(async (serviceRequest) => {
            const [project, requirement, requester] = await Promise.all([
                Project.findOne({ _id: serviceRequest.projectId }).lean(),
                ProjectRequirement.findOne({
                    projectId: serviceRequest.projectId,
                }).lean(),
                User.findById(serviceRequest.userId)
                    .select('name email')
                    .lean(),
            ]);

            return {
                _id: serviceRequest._id,
                projectId: serviceRequest.projectId,
                userId: serviceRequest.userId,
                projectName: project?.projectName || 'Unknown',
                projectType: requirement?.projectType || 'Unknown',
                location: requirement?.location || null,
                budget: requirement?.budget || null,
                expectedStartDate: requirement?.expectedStartDate || null,
                expectedCompletionDate:
                    requirement?.expectedCompletionDate || null,
                createdAt: serviceRequest.createdAt,
            };
        })
    );

    res.status(200).json({ serviceRequests: response });
};
