const VerificationRequests = require('../model/verificationRequests');

exports.getTickets = async (req, res) => {
    try {
        // Get verification statistics
        const verificationStats = await VerificationRequests.aggregate([
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
                    approved: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
                    rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } },
                    contractors: { $sum: { $cond: [{ $eq: ['$type', 'contractor'] }, 1, 0] } },
                    brokers: { $sum: { $cond: [{ $eq: ['$type', 'broker'] }, 1, 0] } },
                    sites: { $sum: { $cond: [{ $eq: ['$type', 'site'] }, 1, 0] } },
                    highPriority: { $sum: { $cond: [{ $eq: ['$priority', 'high'] }, 1, 0] } }
                }
            }
        ]);

        // Get recent verification requests
        const recentActivity = await VerificationRequests.find({})
            .sort({ createdAt: -1 })
            .limit(10)
            .select('type status createdAt requesterId priority')
            .lean();

        const stats = verificationStats[0] || {
            total: 0,
            pending: 0,
            approved: 0,
            rejected: 0,
            contractors: 0,
            brokers: 0,
            sites: 0,
            highPriority: 0
        };

        const dashboardData = {
            totalVerificationRequests: stats.total,
            pendingRequests: stats.pending,
            approvedRequests: stats.approved,
            rejectedRequests: stats.rejected,
            contractorRequests: stats.contractors,
            brokerRequests: stats.brokers,
            siteRequests: stats.sites,
            highPriorityRequests: stats.highPriority,
            recentActivity: recentActivity,
            // Legacy field - consider removing in future versions
            ratings: {
                name: 'rating',
            }
        };

        res.status(200).json(dashboardData);
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Fallback to basic data
        const dashboardData = {
            totalVerificationRequests: 0,
            pendingRequests: 0,
            approvedRequests: 0,
            rejectedRequests: 0,
            contractorRequests: 0,
            brokerRequests: 0,
            siteRequests: 0,
            highPriorityRequests: 0,
            recentActivity: [],
            ratings: {
                name: 'rating',
            }
        };

        res.status(200).json(dashboardData);
    }
};
