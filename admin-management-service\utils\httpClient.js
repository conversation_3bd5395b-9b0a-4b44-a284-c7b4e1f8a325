const axios = require('axios');

// Service URLs based on the API gateway configuration
const SERVICE_URLS = {
    USER_SERVICE: 'http://localhost:3007/user-service/api/v1',
    SITE_SERVICE: 'http://localhost:3006/site-service/api/v1',
    PAYMENT_SERVICE: 'http://localhost:3004/payment-service/api/v1',
    CUSTOMER_SERVICE: 'http://localhost:3002/customer-service/api/v1',
    NOTIFICATION_SERVICE: 'http://localhost:3003/notification-service/api/v1',
    RATING_SERVICE: 'http://localhost:3005/rating-service/api/v1',
};

class HttpClient {
    constructor() {
        this.client = axios.create({
            timeout: 10000, // 10 seconds timeout
            headers: {
                'Content-Type': 'application/json',
            },
        });

        // Add request interceptor for logging
        this.client.interceptors.request.use(
            (config) => {
                console.log(
                    `Making ${config.method?.toUpperCase()} request to: ${config.url}`
                );
                return config;
            },
            (error) => {
                console.error('Request error:', error);
                return Promise.reject(error);
            }
        );

        // Add response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                console.error(
                    'Response error:',
                    error.response?.data || error.message
                );
                return Promise.reject(error);
            }
        );
    }

    // Generic method to make requests to any service
    async makeRequest(service, endpoint, options = {}) {
        const { method = 'GET', data, params, headers = {} } = options;

        const serviceUrl = SERVICE_URLS[service];
        if (!serviceUrl) {
            throw new Error(`Unknown service: ${service}`);
        }

        try {
            const response = await this.client({
                method,
                url: `${serviceUrl}${endpoint}`,
                data,
                params,
                headers,
            });
            return response.data;
        } catch (error) {
            // Handle service unavailable or network errors
            if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
                throw new Error(`Service ${service} is unavailable`);
            }
            throw error;
        }
    }

    // User Service methods
    async getUserById(userId, authToken) {
        return this.makeRequest('USER_SERVICE', `/users/${userId}`, {
            headers: { Authorization: authToken },
        });
    }

    async getAllUsers(params = {}, authToken) {
        return this.makeRequest('USER_SERVICE', '/users', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getAllBrokers(params = {}, authToken) {
        return this.makeRequest('USER_SERVICE', '/brokers/all', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getAllContractors(params = {}, authToken) {
        return this.makeRequest('USER_SERVICE', '/contractors/all', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getBrokerProfile(brokerId, authToken) {
        return this.makeRequest(
            'USER_SERVICE',
            `/brokers/profile/${brokerId}`,
            {
                headers: { Authorization: authToken },
            }
        );
    }

    async getContractorProfile(contractorId, authToken) {
        return this.makeRequest(
            'USER_SERVICE',
            `/contractors/profile/${contractorId}`,
            {
                headers: { Authorization: authToken },
            }
        );
    }

    async updateUserStatus(userId, statusData, authToken) {
        return this.makeRequest('USER_SERVICE', `/users/${userId}/status`, {
            method: 'PUT',
            data: statusData,
            headers: { Authorization: authToken },
        });
    }

    // Site Service methods
    async getAllSites(params = {}, authToken) {
        return this.makeRequest('SITE_SERVICE', '/sites', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getSiteById(siteId, authToken) {
        return this.makeRequest('SITE_SERVICE', `/sites/${siteId}`, {
            headers: { Authorization: authToken },
        });
    }

    async getAllProjects(params = {}, authToken) {
        return this.makeRequest('SITE_SERVICE', '/projects', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getProjectById(projectId, authToken) {
        return this.makeRequest('SITE_SERVICE', `/projects/${projectId}`, {
            headers: { Authorization: authToken },
        });
    }

    // Payment Service methods
    async getAllTransactions(params = {}, authToken) {
        return this.makeRequest('PAYMENT_SERVICE', '/transactions', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getTransactionById(transactionId, authToken) {
        return this.makeRequest(
            'PAYMENT_SERVICE',
            `/transactions/${transactionId}`,
            {
                headers: { Authorization: authToken },
            }
        );
    }

    // Customer Service methods
    async getAllTickets(params = {}, authToken) {
        return this.makeRequest('CUSTOMER_SERVICE', '/tickets', {
            params,
            headers: { Authorization: authToken },
        });
    }

    async getTicketById(ticketId, authToken) {
        return this.makeRequest('CUSTOMER_SERVICE', `/tickets/${ticketId}`, {
            headers: { Authorization: authToken },
        });
    }

    // Notification Service methods
    async sendNotification(notificationData, authToken) {
        return this.makeRequest('NOTIFICATION_SERVICE', '/send', {
            method: 'POST',
            data: notificationData,
            headers: { Authorization: authToken },
        });
    }

    // Rating Service methods
    async getRatings(params = {}, authToken) {
        return this.makeRequest('RATING_SERVICE', '/ratings', {
            params,
            headers: { Authorization: authToken },
        });
    }

    // Helper method to extract auth token from request
    getAuthToken(req) {
        return req.headers.authorization || req.headers.Authorization;
    }

    // Helper method to handle service errors gracefully
    handleServiceError(error, serviceName) {
        if (error.message.includes('unavailable')) {
            return {
                success: false,
                message: `${serviceName} service is currently unavailable`,
                data: null,
            };
        }

        if (error.response?.status === 404) {
            return {
                success: false,
                message: 'Resource not found',
                data: null,
            };
        }

        return {
            success: false,
            message:
                error.response?.data?.message ||
                error.message ||
                'Service error',
            data: null,
        };
    }
}

module.exports = new HttpClient();
