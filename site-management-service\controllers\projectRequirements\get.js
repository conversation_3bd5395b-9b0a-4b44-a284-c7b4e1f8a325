const ExpressError = require('@build-connect/utils/ExpressError');
const Project = require('../../model/project');
const UserRequirement = require('../../model/projectRequirement');
const ServiceRequest = require('../../model/serviceRequest');

exports.getProjectById = async (req, res) => {
    const { projectId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    const project = await Project.findById(projectId).lean();
    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    let hasAccess =
        project.userId.toString() === userId || 
        (project.contractorId && project.contractorId.toString() === userId) || 
        (project.brokerId && project.brokerId.toString() === userId); 
    let accessViaServiceRequest = false;
    if (!hasAccess) {
        const pendingRequest = await ServiceRequest.findOne({
            projectId,
            recipientId: userId,
        }).lean();
        if (pendingRequest) {
            hasAccess = true;
            accessViaServiceRequest = true;
        }
    }

    if (!hasAccess) {
        throw new ExpressError('You do not have access to this project', 403);
    }

    let userRoleInProject = 'owner';
    if (project.contractorId && project.contractorId.toString() === userId) {
        userRoleInProject = 'contractor';
    } else if (project.brokerId && project.brokerId.toString() === userId) {
        userRoleInProject = 'broker';
    } else if (accessViaServiceRequest && (userRole === 'contractor' || userRole === 'broker')) {
        userRoleInProject = userRole;
    }

    const requirement = await UserRequirement.findOne({ projectId }).lean();
    if (!requirement) {
        throw new ExpressError('requirement not found', 404);
    }

    res.status(200).json({
        project: {
            ...project,
            userRoleInProject
        },
        requirement
    });
};

exports.getProjects = async (req, res) => {
    const userId = req.user.id;
    const userRole = req.user.role;

    let projectQuery;

    if (userRole === 'contractor') {
        projectQuery = {
            $or: [
                { userId: userId },
                { contractorId: userId }
            ]
        };
    } else if (userRole === 'broker') {
        projectQuery = {
            $or: [
                { userId: userId },
                { brokerId: userId }
            ]
        };
    } else {
        projectQuery = { userId: userId };
    }

    const projects = await Project.find(projectQuery).lean();

    const projectsWithRequirements = await Promise.all(
        projects.map(async (project) => {
            const requirement = await UserRequirement.findOne({ projectId: project._id }).lean();

            if (requirement) {
                let userRoleInProject = 'owner';
                if (project.contractorId && project.contractorId.toString() === userId) {
                    userRoleInProject = 'contractor';
                } else if (project.brokerId && project.brokerId.toString() === userId) {
                    userRoleInProject = 'broker';
                }

                return {
                    ...project,
                    userRoleInProject,
                    projectType: requirement.projectType,
                    constructionType: requirement.constructionType,
                    location: requirement.location,
                    budget: requirement.budget,
                    designPreferences: requirement.designPreferences,
                    additionalFacilities: requirement.additionalFacilities,
                    brokerAssistanceRequired: requirement.brokerAssistanceRequired,
                    specialInstructions: requirement.specialInstructions,
                    expectedStartDate: requirement.expectedStartDate,
                    expectedCompletionDate: requirement.expectedCompletionDate,
                };
            }

            return {
                ...project,
                projectType: 'Not specified',
                constructionType: 'Not specified',
                location: {
                    address: 'Not specified',
                    city: 'Not specified',
                    state: 'Not specified',
                    pincode: 'Not specified',
                    plotSizeSqFt: null
                },
                budget: {
                    minBudget: null,
                    maxBudget: null
                },
                designPreferences: {
                    floors: null,
                    bedrooms: null,
                    bathrooms: null,
                    parkingRequired: false,
                    gardenRequired: false,
                    vastuCompliance: false
                },
                additionalFacilities: [],
                brokerAssistanceRequired: false,
                specialInstructions: '',
                expectedStartDate: null,
                expectedCompletionDate: null,
            };
        })
    );

    res.status(200).json({ projects: projectsWithRequirements });
};
