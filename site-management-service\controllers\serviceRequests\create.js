const ExpressError = require('@build-connect/utils/ExpressError');
const ServiceRequest = require('../../model/serviceRequest');
const Project = require('../../model/project');
const ProjectRequirement = require('../../model/projectRequirement');

exports.createServiceRequest = async (req, res) => {
    const userId = req.user.id;
    const { recipientId, recipientType } = req.body;
    const { projectId } = req.params;

    const existingRequest = await ServiceRequest.findOne({
        userId,
        recipientId,
        projectId,
    });

    if (existingRequest) {
        throw new ExpressError(
            'You have already sent a service request to this professional for this project',
            409
        );
    }

    if (recipientType === 'contractor') {
        const [project, requirement] = await Promise.all([
            Project.findById(projectId),
            ProjectRequirement.findOne({ projectId }),
        ]);

        if (!project) {
            throw new ExpressError('Project not found', 404);
        }

        if (!requirement) {
            throw new ExpressError('Project requirements not found', 404);
        }

        if (requirement.brokerAssistanceRequired && !project.brokerId) {
            throw new ExpressError(
                'A broker must be assigned to this project before hiring a contractor',
                400
            );
        }
    }

    const serviceRequest = new ServiceRequest({
        userId,
        recipientId,
        projectId,
    });

    await serviceRequest.save();

    res.status(201).json({
        message: 'Service request created successfully',
        serviceRequestId: serviceRequest._id,
    });
};
