const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const httpClient = require('../../utils/httpClient');

// Get all lands with pagination and filters
exports.getAllLands = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            verificationStatus,
            search,
            sortBy = 'createdAt',
            order = 'desc',
        } = req.query;

        // Mock land data - replace with actual API call to site-management-service
        const mockLands = {
            lands: [
                {
                    _id: '507f1f77bcf86cd799439020',
                    title: 'Prime Agricultural Land',
                    location: 'Bangalore Rural',
                    area: 5.5,
                    price: 2500000,
                    status: 'approved',
                    verificationStatus: 'verified',
                    owner: {
                        _id: '507f1f77bcf86cd799439021',
                        name: '<PERSON><PERSON>',
                        email: '<EMAIL>',
                    },
                    createdAt: '2024-01-15T12:00:00Z',
                    verifiedAt: '2024-01-17T10:30:00Z',
                },
                {
                    _id: '507f1f77bcf86cd799439022',
                    title: 'Commercial Plot',
                    location: 'Electronic City',
                    area: 2.0,
                    price: 8000000,
                    status: 'pending',
                    verificationStatus: 'pending',
                    owner: {
                        _id: '507f1f77bcf86cd799439023',
                        name: 'Priya Sharma',
                        email: '<EMAIL>',
                    },
                    createdAt: '2024-01-20T14:15:00Z',
                },
            ],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 17,
                totalLands: 340,
                hasNext: true,
                hasPrev: false,
            },
        };

        res.status(200).json({
            success: true,
            data: mockLands.lands,
            pagination: mockLands.pagination,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch lands',
            error: error.message,
        });
    }
};

// Get land verification queue
exports.getLandVerificationQueue = async (req, res) => {
    try {
        // Mock verification queue data
        const verificationQueue = [
            {
                _id: '507f1f77bcf86cd799439024',
                title: 'Residential Plot in Whitefield',
                location: 'Whitefield, Bangalore',
                area: 1.2,
                price: 3500000,
                owner: {
                    name: 'Amit Patel',
                    email: '<EMAIL>',
                    phone: '+91-9876543215',
                },
                documents: [
                    {
                        type: 'title_deed',
                        url: 'https://example.com/title.pdf',
                        status: 'pending',
                    },
                    {
                        type: 'survey_settlement',
                        url: 'https://example.com/survey.pdf',
                        status: 'pending',
                    },
                    {
                        type: 'tax_receipt',
                        url: 'https://example.com/tax.pdf',
                        status: 'pending',
                    },
                ],
                submittedAt: '2024-01-21T09:30:00Z',
                priority: 'medium',
            },
            {
                _id: '507f1f77bcf86cd799439025',
                title: 'Agricultural Land',
                location: 'Tumkur District',
                area: 10.0,
                price: 5000000,
                owner: {
                    name: 'Lakshmi Devi',
                    email: '<EMAIL>',
                    phone: '+91-9876543216',
                },
                documents: [
                    {
                        type: 'title_deed',
                        url: 'https://example.com/title2.pdf',
                        status: 'verified',
                    },
                    {
                        type: 'survey_settlement',
                        url: 'https://example.com/survey2.pdf',
                        status: 'pending',
                    },
                ],
                submittedAt: '2024-01-20T16:45:00Z',
                priority: 'high',
            },
        ];

        res.status(200).json({
            success: true,
            data: verificationQueue,
            count: verificationQueue.length,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch verification queue',
            error: error.message,
        });
    }
};

// Verify land listing
exports.verifyLandListing = async (req, res) => {
    try {
        const { landId } = req.params;
        const { documentVerifications, notes = '' } = req.body;
        const adminId = req.user.id;

        // Mock API call to site-management-service
        const verificationResult = {
            landId,
            status: 'verified',
            verifiedBy: adminId,
            verifiedAt: new Date(),
            documentVerifications,
            notes,
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'land_verification',
            targetType: 'Site',
            targetId: landId,
            details: { documentVerifications, notes },
        });

        res.status(200).json({
            success: true,
            message: 'Land listing verified successfully',
            data: verificationResult,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to verify land listing',
            error: error.message,
        });
    }
};

// Reject land listing
exports.rejectLandListing = async (req, res) => {
    try {
        const { landId } = req.params;
        const { reason } = req.body;
        const adminId = req.user.id;

        if (!reason) {
            return res.status(400).json({
                success: false,
                message: 'Reason for rejection is required',
            });
        }

        // Mock API call to site-management-service
        const rejectionResult = {
            landId,
            status: 'rejected',
            rejectedBy: adminId,
            rejectedAt: new Date(),
            reason,
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'land_rejection',
            targetType: 'Site',
            targetId: landId,
            details: { reason },
        });

        res.status(200).json({
            success: true,
            message: 'Land listing rejected',
            data: rejectionResult,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to reject land listing',
            error: error.message,
        });
    }
};

// Flag land listing
exports.flagLandListing = async (req, res) => {
    try {
        const { landId } = req.params;
        const { reason } = req.body;
        const adminId = req.user.id;

        if (!reason) {
            return res.status(400).json({
                success: false,
                message: 'Reason for flagging is required',
            });
        }

        // Mock API call to site-management-service
        const flagResult = {
            landId,
            status: 'flagged',
            flaggedBy: adminId,
            flaggedAt: new Date(),
            reason,
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'land_flagging',
            targetType: 'Site',
            targetId: landId,
            details: { reason },
        });

        res.status(200).json({
            success: true,
            message: 'Land listing flagged successfully',
            data: flagResult,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to flag land listing',
            error: error.message,
        });
    }
};
