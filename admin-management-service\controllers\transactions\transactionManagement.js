const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const Transaction = require('../../model/transaction');

// Get all transactions with pagination and filters
exports.getAllTransactions = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            type,
            category,
            userId,
            dateFrom,
            dateTo,
            sortBy = 'createdAt',
            order = 'desc'
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const filter = {};

        // Apply filters
        if (status) filter.status = status;
        if (type) filter.type = type;
        if (category) filter.category = category;
        if (userId) filter.userId = userId;
        if (dateFrom || dateTo) {
            filter.createdAt = {};
            if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
            if (dateTo) filter.createdAt.$lte = new Date(dateTo);
        }

        // Mock transaction data - replace with actual database query
        const mockTransactions = {
            transactions: [
                {
                    _id: '507f1f77bcf86cd799439026',
                    transactionId: 'TXN-**********-0001',
                    userId: '507f1f77bcf86cd799439011',
                    user: { name: 'John Doe', email: '<EMAIL>' },
                    type: 'payment',
                    category: 'site_purchase',
                    amount: 2500000,
                    currency: 'INR',
                    status: 'completed',
                    paymentMethod: 'upi',
                    paymentGateway: 'razorpay',
                    gatewayTransactionId: 'pay_123456789',
                    createdAt: '2024-01-22T10:30:00Z',
                    completedAt: '2024-01-22T10:32:00Z'
                },
                {
                    _id: '507f1f77bcf86cd799439027',
                    transactionId: 'TXN-**********-0002',
                    userId: '507f1f77bcf86cd799439012',
                    user: { name: 'Jane Smith', email: '<EMAIL>' },
                    type: 'commission',
                    category: 'broker_commission',
                    amount: 62500,
                    currency: 'INR',
                    status: 'pending',
                    paymentMethod: 'bank_transfer',
                    createdAt: '2024-01-22T11:15:00Z'
                }
            ],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 45,
                totalTransactions: 890,
                hasNext: true,
                hasPrev: false
            }
        };

        res.status(200).json({
            success: true,
            data: mockTransactions.transactions,
            pagination: mockTransactions.pagination
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch transactions',
            error: error.message
        });
    }
};

// Get transaction details
exports.getTransactionDetails = async (req, res) => {
    try {
        const { transactionId } = req.params;

        // Mock transaction details
        const transactionDetails = {
            _id: transactionId,
            transactionId: 'TXN-**********-0001',
            userId: '507f1f77bcf86cd799439011',
            user: {
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '+91-9876543210'
            },
            type: 'payment',
            category: 'site_purchase',
            amount: 2500000,
            currency: 'INR',
            status: 'completed',
            paymentMethod: 'upi',
            paymentGateway: 'razorpay',
            gatewayTransactionId: 'pay_123456789',
            relatedEntityType: 'Site',
            relatedEntityId: '507f1f77bcf86cd799439020',
            fees: {
                platformFee: 50000,
                brokerCommission: 62500,
                contractorFee: 0,
                taxes: 12500
            },
            metadata: {
                siteTitle: 'Prime Agricultural Land',
                siteLocation: 'Bangalore Rural'
            },
            createdAt: '2024-01-22T10:30:00Z',
            completedAt: '2024-01-22T10:32:00Z',
            timeline: [
                { status: 'initiated', timestamp: '2024-01-22T10:30:00Z' },
                { status: 'processing', timestamp: '2024-01-22T10:30:30Z' },
                { status: 'completed', timestamp: '2024-01-22T10:32:00Z' }
            ]
        };

        res.status(200).json({
            success: true,
            data: transactionDetails
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch transaction details',
            error: error.message
        });
    }
};

// Refund transaction
exports.refundTransaction = async (req, res) => {
    try {
        const { transactionId } = req.params;
        const { amount, reason, refundMethod = 'original' } = req.body;
        const adminId = req.user.id;

        if (!amount || !reason) {
            return res.status(400).json({
                success: false,
                message: 'Amount and reason are required for refund'
            });
        }

        // Mock refund processing
        const refundResult = {
            transactionId,
            refundAmount: amount,
            refundMethod,
            reason,
            processedBy: adminId,
            processedAt: new Date(),
            refundTransactionId: `REF-${Date.now()}`,
            status: 'processing'
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'transaction_refund',
            targetType: 'Transaction',
            targetId: transactionId,
            details: { refundAmount: amount, reason, refundMethod }
        });

        res.status(200).json({
            success: true,
            message: 'Refund initiated successfully',
            data: refundResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to process refund',
            error: error.message
        });
    }
};

// Handle transaction dispute
exports.disputeTransaction = async (req, res) => {
    try {
        const { transactionId } = req.params;
        const { action, resolution, notes } = req.body;
        const adminId = req.user.id;

        const validActions = ['investigate', 'resolve', 'close'];
        if (!validActions.includes(action)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid action. Must be one of: investigate, resolve, close'
            });
        }

        // Mock dispute handling
        const disputeResult = {
            transactionId,
            action,
            resolution,
            notes,
            handledBy: adminId,
            handledAt: new Date(),
            status: action === 'close' ? 'closed' : action === 'resolve' ? 'resolved' : 'investigating'
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'transaction_dispute',
            targetType: 'Transaction',
            targetId: transactionId,
            details: { action, resolution, notes }
        });

        res.status(200).json({
            success: true,
            message: `Transaction dispute ${action}d successfully`,
            data: disputeResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to handle transaction dispute',
            error: error.message
        });
    }
};
